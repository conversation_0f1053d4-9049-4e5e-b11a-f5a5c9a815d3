

# Video Factory 四程序架构设计文档

**版本**: 3.0
**日期**: 2025-07-14
**状态**: 架构确认阶段

---

## 📋 架构概览

### 业务需求分析

基于对V3、V4、V5各版本的深度分析，结合新的业务场景需求，确定四程序分离架构：

**新增业务场景**：
1. **Chrome插件集成**：未来将开发Chrome插件，批量调用视觉理解大模型分析视频内容
2. **数据一致性要求**：本地数据与飞书数据需要100%对齐
3. **双向同步需求**：支持飞书→本地、本地→飞书的双向数据同步

### 四程序架构图

```
input/ → crawl.py → data/crawl/ → download.py → data/download/ → upload.py → 飞书表格
                                                                      ↓
                                                               sync.py ←→ 飞书表格
                                                                      ↓
                                                               Chrome插件 → 视觉AI → 本地JSON更新
```

---

## 🎯 程序功能边界定义

### 1. crawl.py - 数据获取程序

**核心职责**：纯粹的API数据采集引擎

**输入**：
- `input/` 目录下的规则文件（关键词、URL、作者列表）

**输出**：
- `data/crawl/` 目录下的JSON数据文件

**功能边界**：
- ✅ 规则执行（Keywords/Authors/URLs）
- ✅ TikHub API调用和数据标准化
- ✅ JSON文件保存（视频元数据、作者元数据）
- ✅ 智能去重逻辑处理
- ✅ 处理状态记录和失败项记录
- ❌ **不包含**媒体文件下载
- ❌ **不包含**飞书上传功能

### 2. download.py - 媒体下载程序

**核心职责**：专门的媒体文件下载器

**输入**：
- `data/crawl/` 目录下的JSON数据文件

**输出**：
- `data/download/` 目录下的媒体文件

**功能边界**：
- ✅ 读取JSON数据中的媒体URL
- ✅ 并发下载控制（视频、封面、头像）
- ✅ 断点续传机制
- ✅ 文件完整性验证
- ✅ 下载状态管理和重试机制
- ❌ **不包含**数据采集功能
- ❌ **不包含**飞书上传功能

### 3. upload.py - 数据上传程序

**核心职责**：数据上传到飞书表格

**输入**：
- `data/download/` 目录下的完整数据（JSON + 媒体文件）

**输出**：
- 飞书多维表格中的结构化数据

**功能边界**：
- ✅ 读取本地完整数据
- ✅ 数据映射转换（本地格式→飞书格式）
- ✅ 媒体文件上传到飞书云文档
- ✅ 数据同步到飞书多维表格
- ✅ 建立表格间的关联关系
- ✅ 上传状态跟踪
- ❌ **不包含**数据同步功能
- ❌ **不包含**数据一致性检查

### 4. sync.py - 数据同步程序（新增）

**核心职责**：确保本地与飞书数据100%一致性

**输入**：
- 本地数据（`data/` 目录）
- 飞书表格数据

**输出**：
- 同步后的一致性数据状态

**功能边界**：
- ✅ 比对本地数据与飞书数据的差异
- ✅ 双向同步：飞书删除→本地删除
- ✅ 双向同步：本地更新→飞书更新
- ✅ 数据一致性验证和报告
- ✅ 为Chrome插件的数据更新提供同步支持
- ✅ 冲突检测和解决策略
- ❌ **不包含**数据采集功能
- ❌ **不包含**媒体下载功能

---

## 🔄 数据流转设计

### 标准工作流

1. **数据采集阶段**：`crawl.py` 执行规则，获取JSON数据
2. **媒体下载阶段**：`download.py` 下载所有媒体文件
3. **数据上传阶段**：`upload.py` 上传数据到飞书
4. **数据同步阶段**：`sync.py` 确保数据一致性

### 扩展工作流（未来）

1. **Chrome插件分析**：调用视觉AI分析视频内容
2. **本地数据更新**：更新本地video.json文件
3. **自动同步**：`sync.py` 自动同步更新到飞书

### 数据状态管理

- **crawl状态**：记录采集进度和结果
- **download状态**：记录下载进度和结果  
- **upload状态**：记录上传进度和结果
- **sync状态**：记录同步进度和一致性状态

---

## 🎮 程序间协作机制

### 数据传递方式
- **文件系统**：通过共享`data/`目录进行数据传递
- **状态文件**：通过状态文件记录处理进度
- **配置共享**：通过`config.json`共享配置参数

### 错误处理策略
- **失败记录**：通过`failed/`目录记录各阶段失败项
- **重试机制**：支持失败项的重新处理
- **状态恢复**：支持中断后的状态恢复

### 独立性保证
- **程序解耦**：每个程序可独立运行
- **数据隔离**：明确的输入输出边界
- **配置隔离**：各程序有独立的配置段

---

## 🚀 架构优势

### 简单易维护
- **职责单一**：每个程序只负责一个核心功能
- **边界清晰**：输入输出明确，便于调试
- **扩展容易**：新增功能不影响现有程序

### 适合一人公司
- **分步执行**：可以分阶段执行，便于控制
- **错误隔离**：单个程序出错不影响整体流程
- **资源控制**：可以根据需要控制资源使用

### 未来扩展性
- **Chrome插件集成**：sync.py为插件数据更新提供支持
- **AI分析集成**：可以在任意阶段插入AI分析
- **多平台扩展**：架构支持新平台的快速接入

---

*此文档确定了Video Factory项目的最终架构设计，为后续的详细功能设计提供基础。*
