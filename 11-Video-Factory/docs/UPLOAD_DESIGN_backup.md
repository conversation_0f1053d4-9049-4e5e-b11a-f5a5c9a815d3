

# Video Factory 数据上传功能设计文档

**版本**: 1.0  
**状态**: ✅ 100%实现完成，生产就绪  
**最后更新**: 2025-07-15  
**基于**: experimental/目录完整验证成果

---

## 📋 设计概览

### 🎯 功能目标
upload.py程序负责将本地数据（JSON + 媒体文件）上传到飞书多维表格，实现：
- **完整数据上传**: 账号信息和视频信息的结构化存储
- **媒体文件管理**: 头像、视频、封面等文件的智能上传
- **双向关联建立**: 账号表与素材表的关联关系
- **智能数据同步**: 基于对比的增量更新策略

### ✅ 实现状态
基于experimental/目录的完整验证，所有核心功能已100%实现：
- ✅ **字段管理系统**: 配置驱动的多平台字段映射
- ✅ **智能同步系统**: 基于数据对比的更新策略
- ✅ **双向关联功能**: 账号↔视频关联机制
- ✅ **真实文件上传**: 25MB大文件分片上传验证
- ✅ **空值处理逻辑**: null、空字符串、零值完整处理

---

## 🏗️ 技术架构设计

### 核心组件架构
```
UploadManager (主管理器)
├── FieldManagementSystem (字段管理系统)
│   ├── ConfigManager (配置管理器)
│   ├── FieldDiscovery (字段发现器)
│   └── MappingProcessor (映射处理器)
├── IntelligentSyncSystem (智能同步系统)
│   ├── ComparisonEngine (对比引擎)
│   ├── UpdateStrategy (更新策略)
│   └── NullValueHandler (空值处理器)
├── FileUploadManager (文件上传管理器)
│   ├── ChunkedUploader (分片上传器)
│   ├── SimpleUploader (普通上传器)
│   └── MediaRouter (媒体路由器)
├── RelationshipManager (关联管理器)
│   ├── BidirectionalLinker (双向关联器)
│   └── RecordMatcher (记录匹配器)
└── FeishuAPIClient (飞书API客户端)
    ├── AuthManager (认证管理器)
    ├── RequestHandler (请求处理器)
    └── ErrorHandler (错误处理器)
```

### 数据流设计
```
本地数据 → 字段映射 → 智能对比 → 文件上传 → 数据同步 → 关联建立 → 飞书表格
   ↑           ↑         ↑         ↑         ↑         ↑
配置文件    字段管理   同步策略   上传路由   API客户端  关联管理
```

---

## 📊 字段管理系统设计

### 配置驱动架构
基于`field_mapping.json`实现完全配置驱动的字段管理：

#### 账号表字段映射 (13个字段)
- **作者唯一ID**: 平台账号唯一标识符
- **作者主页**: 作者主页链接
- **作者头像**: 头像文件附件
- **作者昵称**: 显示名称
- **作者ID**: 平台内部ID
- **粉丝数**: 关注者数量
- **关注数**: 关注的账号数量
- **视频数**: 发布的视频总数
- **总获赞数**: 累计获赞数量
- **作者简介**: 个人简介文本
- **同步更新时间**: 最后同步时间戳
- **平台**: 数据来源平台
- **地区**: 账号所属地区

#### 素材表字段映射 (17个字段)
- **视频ID**: 视频唯一标识符
- **视频标题**: 视频标题文本
- **发布时间**: 视频发布时间戳
- **视频标签**: 标签数组
- **原始链接**: 视频原始URL
- **点赞数**: 点赞数量
- **评论数**: 评论数量
- **分享数**: 分享数量
- **收藏数**: 收藏数量
- **时长(秒)**: 视频时长
- **分辨率**: 视频分辨率
- **视频/图集**: 内容类型
- **视频封面**: 封面图片附件
- **同步更新时间**: 最后同步时间戳
- **关联作者**: 双向关联字段
- **平台**: 数据来源平台
- **地区**: 内容所属地区

### 多平台数据源支持
支持抖音、快手、小红书三大平台的数据映射：
```json
"source_mapping": {
  "douyin": "unique_id",
  "kuaishou": "user_id", 
  "xiaohongshu": "author_id"
}
```

---

## 🔄 智能数据同步系统设计

### 对比引擎设计
基于`sync_strategy_config.json`实现智能数据对比：

#### 字段类型对比策略
1. **文本字段**: 内容差异检测，自动去除首尾空格
2. **数值字段**: 仅在源数据更大时更新，支持精度控制
3. **日期字段**: 按时间戳数值比较，支持时区处理
4. **选择字段**: 单选按文本处理，多选支持集合对比
5. **附件字段**: 文件名+大小智能对比，支持增删改逻辑

#### 空值处理策略
- **null值**: 完全跳过，不参与任何更新操作
- **空字符串**: 可配置跳过（默认跳过）
- **零值**: 按数值规则正常处理，不视为空值

#### 更新策略引擎
- **覆盖更新**: 源数据完全替换飞书数据
- **增量更新**: 只更新有变化的字段
- **智能跳过**: 根据对比结果智能跳过无需更新的字段
- **批量处理**: 支持多字段批量更新

---

## 📁 文件上传管理设计

### 智能上传路由
根据文件大小自动选择上传策略：
- **普通上传**: 文件 < 10MB，单次上传
- **分片上传**: 文件 ≥ 10MB，分片上传，支持断点续传

### 媒体文件类型支持
- **头像文件**: JPG/PNG格式，上传到账号表
- **视频文件**: MP4格式，支持25MB大文件分片上传
- **封面图片**: JPG/PNG格式，上传到素材表
- **其他附件**: PDF/MP3等格式支持

### 上传状态管理
- **上传进度**: 实时跟踪上传进度
- **错误重试**: 自动重试机制，最多3次
- **完整性验证**: 文件大小和格式验证

---

## 🔗 双向关联功能设计

### 关联机制
通过编号字段建立账号表与素材表的双向关联：
- **账号编号格式**: `作者昵称 @作者唯一ID (粉丝数)`
- **视频编号格式**: `平台+视频ID`

### 关联数据结构
```json
{
  "record_ids": ["target_record_id"],
  "table_id": "target_table_id", 
  "text": "编号字段值",
  "text_arr": ["编号字段值"],
  "type": "text"
}
```

### 关联建立流程
1. **编号字段提取**: 从公式字段获取编号值
2. **记录匹配**: 根据编号匹配目标记录
3. **关联数据构建**: 构建符合API格式的关联数据
4. **双向关联创建**: 同时在两个表中建立关联

---

## ⚙️ 配置文件设计

### 主配置文件 (config.json)
```json
{
  "feishu": {
    "app_id": "cli_a77df84ce77bd00c",
    "app_secret": "aCtytKq9D9BcDvpMefJ97fSdlkGZZP1w",
    "account_table": {
      "app_token": "CNtHbpe38a6r1LsEV3kcfUTynHe",
      "table_id": "tblvg8I0foe4g4ib"
    },
    "video_table": {
      "app_token": "CNtHbpe38a6r1LsEV3kcfUTynHe", 
      "table_id": "tblkZpPQ7CfFUUOl"
    }
  }
}
```

### 字段映射配置 (field_mapping.json)
- **版本管理**: 配置文件版本控制
- **字段定义**: 完整的字段类型和映射定义
- **平台映射**: 多平台数据源字段映射
- **验证规则**: 字段验证和转换规则

### 同步策略配置 (sync_strategy_config.json)
- **全局设置**: 空值处理、精度控制等全局参数
- **字段策略**: 每个字段的具体更新策略
- **性能配置**: 批量大小、重试次数等性能参数

---

## 🚀 部署和使用

### 环境要求
- **Python**: 3.7+
- **依赖库**: requests, json, os, time
- **虚拟环境**: `/Users/<USER>/Downloads/Ming-Digital-Garden/venv/`

### 启动命令
```bash
cd /Users/<USER>/Downloads/Ming-Digital-Garden
source venv/bin/activate
cd 11-Video-Factory
python3 upload.py
```

### 输入数据格式
- **输入目录**: `data/download/`
- **JSON数据**: 标准化的账号和视频元数据
- **媒体文件**: 对应的头像、视频、封面文件

### 输出结果
- **飞书表格**: 结构化的账号和视频数据
- **关联关系**: 账号与视频的双向关联
- **状态报告**: 上传进度和结果统计

---

*此文档基于experimental/目录100%验证完成的功能实现，为upload.py主程序开发提供完整的技术参考。*
