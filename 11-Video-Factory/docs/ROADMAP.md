

# **Video Factory 项目路线图与架构概览 (V4.0)**

**文档目的:** 本文档提供Video Factory项目V4.0版本的整体蓝图、核心理念、四程序分离架构和完整工作流的高级别概述。它是项目所有后续详细设计与开发的**最高战略指导和沟通基准**，为AI编程助手提供完整的项目上下文和开发方向。

## **1. 项目愿景与核心目标 (V4.0)**

### **愿景**
构建一个以**API为驱动**、**高度模块化**、**可扩展**的自动化工作流，用于从多平台（抖音、TikTok，并为小红书、YouTube等预留扩展）高效地采集、处理、同步高质量的视频及作者数据，为下游的AI内容分析与再创作提供坚实的数据基石。

### **核心目标 (V4.0 四程序分离重构)**
1. **架构现代化**: 彻底重构为稳定、高效、易于维护的**四程序分离API驱动架构**
2. **职责精确分离**: 明确划分**数据采集(crawl.py)**、**媒体下载(download.py)**、**数据上传(upload.py)**、**数据同步(sync.py)**四大核心程序的职责，实现高度解耦
3. **多平台扩展性**: 建立统一的平台适配层和标准化的内部数据模型，使未来接入新平台的成本降至最低
4. **数据一致性保证**: 通过sync.py确保本地与飞书数据100%一致，支持双向同步
5. **AI集成就绪**: 为Chrome插件+视觉AI分析提供完整的数据基础和同步机制

## **2. 核心理念与设计原则 (V4.0)**

### **API驱动 (API-First)**
所有外部数据获取的**唯一且权威的来源**是`api.tikhub.io`服务。这保证了数据获取的稳定性、效率和丰富性。

### **四程序分离 (Four-Program Separation)**
- **采集层 (crawl.py)**: 专注于纯粹的**数据采集器**，根据规则调用API，获取原始数据并存储到本地
- **下载层 (download.py)**: 专注于**媒体文件下载器**，支持并发下载和断点续传
- **上传层 (upload.py)**: 专注于**数据上传器**，将本地数据同步到飞书表格
- **同步层 (sync.py)**: 专注于**数据一致性保证器**，确保本地与飞书数据100%一致

### **模块化与可扩展性 (Modular & Extensible)**
- 采用清晰的分层架构：规则执行层 → 平台适配层 → 任务处理层
- **平台适配器**是核心扩展点，新增平台支持只需添加新的适配器模块
- 模块间通过标准接口通信，降低耦合度

### **模型驱动 (Model-Driven)**
项目内部定义一套标准的、与平台无关的**数据模型**，如`StandardVideo`和`StandardAuthor`。所有原始数据在处理前都必须映射到这些标准模型，确保内部数据流的统一和规整。

### **配置驱动 (Configuration-Driven)**
核心参数、路径、API密钥、以及各规则的独立参数均在唯一的`config.json`文件中定义，实现逻辑与配置分离。

### **健壮性与容错 (Robustness & Fault Tolerance)**
- API交互层包含完善的重试和错误处理机制
- 建立统一的**失败记录工作流**，对采集和上传各阶段的失败项进行分类记录
- 支持断点续传和增量处理

## **3. 工作流阶段分解 (V4.0 四程序架构)**

### **阶段 1: 多平台原始数据采集与存储 (crawl.py)**
**任务**:
- 根据用户选择的**规则**(关键词、URL列表、作者列表)、**平台**、**区域**及**任务类型**(新爬取、仅更新元数据、仅更新视频)
- **数据源**: 完全依赖对`api.tikhub.io`的API调用
- **核心处理逻辑**:
  - **规则执行器**根据用户选择，调用相应平台的**适配器**
  - **平台适配器**负责调用`api.tikhub.io`上特定平台的API端点，获取**原始API响应JSON**
  - **任务处理器**根据用户选择的任务类型，对原始API响应JSON执行相应操作（去重、创建/覆盖JSON文件、更新状态库）

**产出 (JSON数据)**:
- 原始作者元数据JSON文件 (存于`data/crawl/author_data/`)
- 原始视频元数据JSON文件 (存于`data/crawl/video_data/`)
- 更新后的状态文件 (存于`data/crawl/state/`)
- 采集失败的条目记录 (存于`failed/crawl/`)

### **阶段 2: 媒体文件批量下载 (download.py)**
**任务**:
- 读取`crawl.py`产生的JSON数据文件
- 并发下载视频、封面、头像等媒体文件
- 支持断点续传和质量选择

**产出 (媒体文件)**:
- 作者头像 (存于`data/download/author_data/avatar/`)
- 视频封面 (存于`data/download/video_data/cover/`)
- 视频文件 (存于`data/download/video_data/video/`)
- 图集图片 (存于`data/download/video_data/image/{video_id}/`)
- 下载状态和进度记录

### **阶段 3: 数据处理、映射与飞书同步 (upload.py)**
**任务**:
- 读取`download.py`产生的完整数据(JSON+媒体文件)
- 将不同平台的原始JSON数据**映射**为项目内部统一的**标准数据模型**
- 将标准化的数据结构化地上传并存储到飞书多维表格(账号表、素材表)，并建立双向关联
- 智能管理飞书内的动态选项(如标签、平台、区域)

**产出**:
- 更新后的飞书表格数据
- 归档到`data/uploaded/`的文件
- 上传失败的条目记录 (存于`failed/upload/`)
- 飞书机器人通知

### **阶段 4: 数据一致性同步与管理 (sync.py)**
**任务**:
- 比对本地数据与飞书数据的差异
- 执行双向同步：飞书删除→本地删除，本地更新→飞书更新
- 为Chrome插件的数据更新提供同步支持
- 确保数据100%一致性

**产出**:
- 同步状态报告
- 数据一致性验证结果
- 冲突解决记录
- 为AI分析准备的清洁数据集

## **4. AI集成与扩展规划 (阶段5-11保留完整)**

### **阶段 5: AI 视频内容理解与结构化 (Video Content AI Analysis & Structuring)**
**任务**: 利用AI大模型服务，对飞书中的视频进行深度分析。提取关键内容信息，并将结构化的分析结果同步回**对应的飞书素材表记录**中。
**工具**: 独立的调用脚本/服务或**潜在的浏览器插件(对接LLM与飞书API)**。
**输入**: 飞书素材表记录/本地视频文件(位于`data/downloads/`)。
**输出**: 更新到飞书素材表记录中的结构化视频理解数据。

### **阶段 6: AI 分析结果入库与触发 (AI Analysis Result Ingestion & Trigger - Feishu)**
**任务**: 确保阶段5的分析结果正确写入飞书。
**工具**: 主要依赖飞书平台能力(API/自动化)。
**输入**: 阶段5输出的结构化数据。
**输出**: 包含深度理解结果的飞书表格数据，可能触发下游流程。

### **阶段 7: AI 内容二次创作策略生成 (AI Content Re-creation Strategy - Feishu)**
**任务**: 基于飞书中的原始数据和AI视频理解结果，生成二次创作所需的核心元素。
**工具**: 飞书自动化、飞书智能伙伴、定制脚本。
**输入**: 飞书表格数据(含阶段6结果)。
**输出**: 二次创作所需的文本内容，存储在飞书表格对应记录中。

### **阶段 8: 视觉主图批量生成 (Visual Asset Generation - Midjourney Integration)**
**任务**: 将阶段7生成的MJ提示词，批量提交给Midjourney进行图像生成。
**工具**: **潜在的浏览器插件(对接飞书与Midjourney)**或其他MJ自动化交互工具。
**输入**: 飞书表格中的MJ提示词。
**输出**: 在Midjourney中生成的候选主图。

### **阶段 9: 主图人工筛选 (Manual Asset Selection)**
**任务**: 人工从MJ生成的图片中筛选出满意的主图。
**工具**: 人工操作。
**输入**: Midjourney生成的图片。
**输出**: 选定的主图文件(本地)。

### **阶段 10: 选定主图入库与关联 (Selected Asset Ingestion - Feishu)**
**任务**: 将人工选定的主图文件上传回飞书，并与对应的素材记录关联。
**工具**: 可能通过手动上传，或阶段8的**浏览器插件/工具提供回传功能**。
**输入**: 本地选定的主图文件。
**输出**: 飞书表格中包含选定主图附件及关联的数据。

### **阶段 11: 分镜视频素材生成 (Shot/Sequence Generation - Image-to-Video)**
**任务**: 基于选定的主图，调用图生视频AI服务，批量生成视频分镜片段。
**工具**: 调用相关图生视频API的脚本/服务。
**输入**: 飞书中的主图附件、可能的文本提示词。
**输出**: 生成的视频分镜片段(本地或云端)。

### **阶段 12: 分镜素材人工筛选 (Manual Shot Selection)**
**任务**: 人工从生成的视频分镜中挑选合适的片段。
**工具**: 人工操作/视频预览工具。
**输入**: 生成的视频分镜片段。
**输出**: 选定的、有序的视频分镜素材。

### **阶段 13: 最终剪辑与发布 (Final Video Editing & Publishing)**
**任务**: 使用选定的分镜素材等，在专业视频编辑软件中进行最终的人工剪辑、合成并发布。
**工具**: 专业视频剪辑软件。
**输入**: 选定的分镜素材、脚本、主图、音频等。
**输出**: 最终成品短视频。

## **5. 关键组件/模块 (V4.0)**

**主程序**: `crawl.py`, `download.py`, `upload.py`, `sync.py`。
**核心配置**: `config.json`。
**源代码 (`src/`)**:
- **`core/`**: `api_client`, `data_manager`, `media_downloader`等基础服务
- **`rules/`**: `keywords_executor`, `urls_executor`, `authors_executor`等规则逻辑
- **`platforms/`**: `douyin_adapter`, `tiktok_adapter`等平台适配器
- **`tasks/`**: `new_crawl_processor`, `update_meta_processor`等任务处理器
- **`models/`**: `StandardVideo`, `StandardAuthor`等Pydantic数据模型
**核心外部依赖**: `api.tikhub.io`。
**数据与流程中枢**: 飞书平台。

## **6. 数据流 (四程序架构 - V4.0)**

```
用户输入(input/) → crawl.py → data/crawl/ → download.py → data/download/ → upload.py → 飞书表格
                                                                           ↓
                                                                    sync.py ←→ 飞书表格
                                                                           ↓
                                                                Chrome插件 → 视觉AI → 本地JSON更新
```

**详细数据流**:
1. 用户从`input/`选择规则文件 + 交互选择规则/平台/区域/任务类型
2. `crawl.py`调用TikHub API → 标准JSON数据存于`data/crawl/`
3. `download.py`读取JSON → 下载媒体文件存于`data/download/`
4. `upload.py`读取完整数据 → 上传到飞书表格
5. `sync.py`确保数据一致性 → 双向同步本地与飞书
6. 未来Chrome插件 → 视觉AI分析 → 更新本地JSON → sync.py同步到飞书

## **7. 对AI编程助手的价值**

### **项目理解支持**
- 提供完整的项目愿景和技术架构上下文
- 明确四程序分离的设计理念和职责边界
- 为AI助手提供一致的开发方向指导

### **开发决策依据**
- 明确技术选择的业务背景和架构原则
- 提供扩展性和可维护性的设计约束
- 为功能优先级和开发顺序提供指导

### **长期一致性保证**
- 确保AI助手在长期开发过程中保持项目愿景一致
- 提供架构演进的清晰路径
- 为代码重构和优化提供业务上下文

### **协作效率提升**
- 减少AI助手对项目背景的重复询问
- 提供标准化的术语和概念定义
- 为代码生成和问题解决提供完整上下文

---

*此文档是Video Factory项目的核心指导文档，为所有后续开发工作提供战略方向和技术框架。*
