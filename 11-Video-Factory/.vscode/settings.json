{
    // Video-Factory项目Python配置 (适配子项目目录结构)
    "python.defaultInterpreterPath": "../venv/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.analysis.extraPaths": [
        "./src"
    ],
    "python.analysis.autoSearchPaths": true,
    "python.analysis.autoImportCompletions": true,

    // 新的格式化和代码检查配置
    "[python]": {
        "editor.defaultFormatter": "ms-python.python",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.organizeImports": "explicit"
        }
    },

    // 搜索时排除venv和Package目录
    "search.exclude": {
        "../venv": true,
        "../Package": true
    },

    // 终端设置
    "terminal.integrated.cwd": "${workspaceFolder}",
    "terminal.integrated.defaultProfile.osx": "zsh (venv)",
    "terminal.integrated.profiles.osx": {
        "zsh (venv)": {
            "path": "zsh",
            "args": ["-c", "cd ${workspaceFolder} && source ../venv/bin/activate && exec zsh"]
        }
    },

    // Git设置
    "git.ignoreLimitWarning": true,

    // 文件关联设置 - 修复.coveragerc等配置文件被误识别的问题
    "files.associations": {
        ".coveragerc": "ini",
        "*.coveragerc": "ini",
        ".env": "properties",
        ".env.*": "properties",
        ".gitignore": "ignore",
        ".augment-guidelines": "markdown"
    },

    // cmd+p快速打开文件时排除的目录和文件
    "files.exclude": {
        "../venv": true,
        "../Package": true,
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/.pytest_cache": true,
        "**/.coverage": true,
        "**/htmlcov": true
    }
}
