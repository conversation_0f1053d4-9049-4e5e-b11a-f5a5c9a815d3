{"version": "1.0", "description": "智能数据同步更新策略配置", "last_updated": "2025-07-14", "global_settings": {"enable_null_skip": true, "enable_empty_skip": true, "text_trim_whitespace": true, "number_precision": 0.001, "file_hash_check": false, "atomic_update": true, "max_retries": 3, "update_batch_size": 10, "enable_update_log": true}, "field_strategies": {"account": {"作者唯一ID": {"strategy": "text_overwrite", "enable_update": true, "priority": "high"}, "作者昵称": {"strategy": "text_overwrite", "enable_update": true, "priority": "medium"}, "作者简介": {"strategy": "text_overwrite", "enable_update": true, "priority": "low"}, "粉丝数": {"strategy": "number_increase_only", "enable_update": true, "priority": "high", "min_increase_threshold": 100}, "关注数": {"strategy": "number_increase_only", "enable_update": true, "priority": "medium", "min_increase_threshold": 10}, "视频数": {"strategy": "number_increase_only", "enable_update": true, "priority": "medium", "min_increase_threshold": 1}, "总获赞数": {"strategy": "number_increase_only", "enable_update": true, "priority": "high", "min_increase_threshold": 1000}, "作者主页": {"strategy": "url_overwrite", "enable_update": true, "priority": "low"}, "作者头像": {"strategy": "attachment_smart", "enable_update": true, "priority": "low", "max_file_count": 3, "allowed_extensions": [".jpg", ".jpeg", ".png"], "max_file_size_mb": 10}, "平台": {"strategy": "select_overwrite", "enable_update": false, "priority": "critical", "reason": "平台信息不应变更"}, "地区": {"strategy": "select_overwrite", "enable_update": true, "priority": "low"}, "同步更新时间": {"strategy": "datetime_always_update", "enable_update": true, "priority": "critical"}}, "video": {"视频ID": {"strategy": "text_overwrite", "enable_update": true, "priority": "critical"}, "视频标题": {"strategy": "text_overwrite", "enable_update": true, "priority": "medium"}, "发布时间": {"strategy": "datetime_no_update", "enable_update": false, "priority": "critical", "reason": "发布时间不应变更"}, "视频标签": {"strategy": "multiselect_merge", "enable_update": true, "priority": "low", "max_tags": 10}, "原始链接": {"strategy": "url_overwrite", "enable_update": true, "priority": "medium"}, "点赞数": {"strategy": "number_increase_only", "enable_update": true, "priority": "high", "min_increase_threshold": 10}, "评论数": {"strategy": "number_increase_only", "enable_update": true, "priority": "high", "min_increase_threshold": 5}, "分享数": {"strategy": "number_increase_only", "enable_update": true, "priority": "medium", "min_increase_threshold": 1}, "收藏数": {"strategy": "number_increase_only", "enable_update": true, "priority": "medium", "min_increase_threshold": 1}, "时长(秒)": {"strategy": "number_overwrite", "enable_update": true, "priority": "low"}, "分辨率": {"strategy": "text_overwrite", "enable_update": true, "priority": "low"}, "视频/图集": {"strategy": "attachment_smart", "enable_update": true, "priority": "medium", "max_file_count": 1, "allowed_extensions": [".mp4", ".mov", ".avi", ".jpg", ".jpeg", ".png"], "max_file_size_mb": 500}, "视频封面": {"strategy": "attachment_smart", "enable_update": true, "priority": "low", "max_file_count": 1, "allowed_extensions": [".jpg", ".jpeg", ".png"], "max_file_size_mb": 20}, "同步更新时间": {"strategy": "datetime_always_update", "enable_update": true, "priority": "critical"}, "关联作者": {"strategy": "relation_no_update", "enable_update": false, "priority": "critical", "reason": "关联关系通过专门API管理"}, "平台": {"strategy": "select_overwrite", "enable_update": false, "priority": "critical", "reason": "平台信息不应变更"}, "地区": {"strategy": "select_overwrite", "enable_update": true, "priority": "low"}}}, "update_strategies": {"text_overwrite": {"description": "文本字段覆盖更新", "condition": "source != feishu", "action": "overwrite", "null_handling": "skip", "empty_handling": "skip"}, "number_increase_only": {"description": "数值字段仅增长更新", "condition": "source > feishu + threshold", "action": "overwrite", "null_handling": "skip", "zero_handling": "skip"}, "number_overwrite": {"description": "数值字段覆盖更新", "condition": "source != feishu", "action": "overwrite", "null_handling": "skip"}, "datetime_always_update": {"description": "日期时间字段总是更新", "condition": "always", "action": "overwrite", "null_handling": "use_current_time"}, "datetime_no_update": {"description": "日期时间字段不更新", "condition": "never", "action": "skip", "reason": "保护性字段"}, "select_overwrite": {"description": "选择字段覆盖更新", "condition": "source != feishu", "action": "overwrite", "null_handling": "skip"}, "multiselect_merge": {"description": "多选字段合并更新", "condition": "source has new items", "action": "merge", "null_handling": "skip", "duplicate_handling": "remove"}, "multiselect_overwrite": {"description": "多选字段覆盖更新", "condition": "source != feishu", "action": "overwrite", "null_handling": "skip"}, "url_overwrite": {"description": "链接字段覆盖更新", "condition": "source.link != feishu.link", "action": "overwrite", "null_handling": "skip"}, "attachment_smart": {"description": "附件字段智能更新", "condition": "complex_file_comparison", "action": "smart_merge", "rules": ["source_exists_feishu_not: append", "same_name_source_larger: overwrite", "same_name_source_smaller: skip", "feishu_exists_source_not: keep"]}, "relation_no_update": {"description": "关联字段不更新", "condition": "never", "action": "skip", "reason": "通过专门API管理"}}, "error_handling": {"on_field_update_failure": "continue", "on_record_update_failure": "retry", "max_field_errors_per_record": 5, "enable_rollback": false, "log_all_errors": true}, "performance_settings": {"enable_batch_get": true, "batch_size": 20, "enable_field_cache": true, "cache_ttl_seconds": 300, "enable_parallel_processing": false, "max_concurrent_updates": 5}, "monitoring": {"enable_update_metrics": true, "enable_performance_tracking": true, "log_level": "info", "alert_on_high_failure_rate": true, "failure_rate_threshold": 0.1}}